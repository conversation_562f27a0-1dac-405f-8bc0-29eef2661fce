import { <PERSON>, CardFooter, Image, Button } from '@heroui/react';
import { FaRegStar } from 'react-icons/fa';

const Exploretheworld = () => {
  const data = [
    {
      id: 1,
      name: 'Island',
      image: 'https://heroui.com/images/hero-card.jpeg',
      rating: 5,
    },
    {
      id: 2,
      name: 'Mountains',
      image: 'https://heroui.com/images/album-cover.png',
      rating: 4,
    },
    {
      id: 3,
      name: 'Desert',
      image: 'https://heroui.com/images/hero-card.jpeg',
      rating: 3,
    },
    {
      id: 4,
      name: '<PERSON>',
      image: 'https://heroui.com/images/album-cover.png',
      rating: 5,
    },
    {
      id: 5,
      name: 'Cityscape',
      image: 'https://heroui.com/images/hero-card.jpeg',
      rating: 4,
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <div className="space-y-2 w-[200px]">
          <p className="text-xl font-bold">Explore the world</p>
          <p className="text-sm text-subtitle max-w-md">
            A list of the top 75 Best Tourist Places to See in the world for a
            perfect holiday or a trip.
          </p>
          <button
            type="button"
            className="px-5 py-2 rounded-full bg-primary text-white text-sm font-semibold hover:opacity-90 transition"
          >
            View More
          </button>
        </div>
        {data.map(place => (
          <Card
            key={place.id}
            isFooterBlurred
            className="border-none rounded-md"
            radius="lg"
          >
            <Image
              alt={place.name}
              className="object-cover rounded-md"
              height={290}
              src={place.image}
              width={220}
            />
            <CardFooter className="flex flex-col items-start before:bg-white/10 border-white/20 border-1 overflow-hidden py-1 absolute before:rounded-xl rounded-large bottom-1 w-[calc(100%_-_8px)] ml-1 z-10">
              <p className="font-bold text-base text-white">{place.name}</p>
              <div className="flex flex-row gap-1">
                {Array(5)
                  .fill(0)
                  .map((_, i) => (
                    <FaRegStar
                      key={i}
                      fill={i < place.rating ? '#FFC107' : '#ccc'}
                    />
                  ))}
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default Exploretheworld;
